-- StealADino Script with Rayfield UI
local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Services
local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Variables
local LocalPlayer = Players.LocalPlayer
local Character = LocalPlayer.Character or LocalPlayer.CharacterAdded:Wait()
local Humanoid = Character:WaitForChild("Humanoid")
local HumanoidRootPart = Character:WaitForChild("HumanoidRootPart")

local MyBaseName = nil
local MyBaseNumber = nil
local DinosaurList = {}
local AutoLockEnabled = false

-- Functions
local function FindMyBase()
    local targetOwner = "WhyUSOmaddKid"

    for i = 1, 9 do
        local basePath = Workspace.Bases:FindFirstChild("Base" .. i)
        if basePath then
            local ownerValue = basePath:FindFirstChild("Owner")
            if ownerValue and ownerValue.ClassName == "ObjectValue" then
                local ownerValueData = ownerValue.Value
                local ownerName = ""

                -- Handle different types of Value (Player object or string)
                if typeof(ownerValueData) == "Instance" and ownerValueData:IsA("Player") then
                    ownerName = ownerValueData.Name
                elseif type(ownerValueData) == "string" then
                    ownerName = ownerValueData
                else
                    ownerName = tostring(ownerValueData)
                end

                -- Debug print untuk melihat value yang ditemukan
                print("Base" .. i .. " Owner Value:", ownerValueData)
                print("Owner Name:", ownerName)
                print("Target Owner:", targetOwner)
                print("Match:", ownerName == targetOwner)

                if ownerName == targetOwner then
                    MyBaseName = "Base" .. i
                    MyBaseNumber = i
                    print("Found my base:", MyBaseName)
                    return MyBaseName
                end
            else
                print("Base" .. i .. " - Owner not found or wrong class")
            end
        else
            print("Base" .. i .. " not found")
        end
    end
    print("My base not found!")
    return nil
end

local function TeleportToPosition(position)
    if HumanoidRootPart then
        HumanoidRootPart.CFrame = CFrame.new(position)
    end
end

local function TeleportToMyBase()
    if MyBaseName then
        local myBase = Workspace.Bases:FindFirstChild(MyBaseName)
        if myBase then
            local collectZone = myBase:FindFirstChild("CollectZone")
            if collectZone then
                local collect = collectZone:FindFirstChild("Collect")
                if collect then
                    TeleportToPosition(collect.Position + Vector3.new(0, 5, 0))
                end
            end
        end
    end
end

local function AutoLockBase()
    if MyBaseName then
        -- Method 1: Using RemoteEvent (Primary method)
        pcall(function()
            local args = {
                true,
                "You locked your base for 60 seconds!"
            }
            game.ReplicatedStorage.Send:FireServer(unpack(args))
            print("Auto Lock: Fired RemoteEvent (Send) for", MyBaseName)
            return true
        end)

        -- Method 2: Alternative RemoteEvent names
        pcall(function()
            local remoteEvents = {
                "LockBase",
                "BaseLock",
                "Lock",
                "SendLock"
            }

            for _, eventName in pairs(remoteEvents) do
                local remoteEvent = game.ReplicatedStorage:FindFirstChild(eventName)
                if remoteEvent and remoteEvent:IsA("RemoteEvent") then
                    local args = {
                        true,
                        "You locked your base for 60 seconds!"
                    }
                    remoteEvent:FireServer(unpack(args))
                    print("Auto Lock: Fired RemoteEvent (" .. eventName .. ") for", MyBaseName)
                    return true
                end
            end
        end)

        -- Method 3: TouchTransmitter fallback
        local myBase = Workspace.Bases:FindFirstChild(MyBaseName)
        if myBase then
            local lockBase = myBase:FindFirstChild("LockBase")
            if lockBase then
                local touchInterest = lockBase:FindFirstChild("TouchInterest")
                if touchInterest and touchInterest.ClassName == "TouchTransmitter" then
                    pcall(function()
                        if firetouchtransmitter then
                            firetouchtransmitter(touchInterest)
                            print("Auto Lock: Fired TouchTransmitter (Fallback) for", MyBaseName)
                            return true
                        end
                    end)

                    pcall(function()
                        if getconnections then
                            local connections = getconnections(touchInterest.Touched)
                            for _, connection in pairs(connections) do
                                if connection.Function then
                                    connection.Function()
                                    print("Auto Lock: Fired TouchTransmitter (Connections) for", MyBaseName)
                                    return true
                                end
                            end
                        end
                    end)
                end
            end
        end

        return true
    else
        print("Auto Lock: MyBaseName is nil")
        return false
    end
end

local function ScanDinosaurs()
    DinosaurList = {}
    
    for i = 1, 9 do
        local basePath = Workspace.Bases:FindFirstChild("Base" .. i)
        if basePath and basePath.Name ~= MyBaseName then
            local pads = basePath:FindFirstChild("Pads")
            if pads then
                for _, pad in pairs(pads:GetChildren()) do
                    if pad.Name:match("Pad%d+") then
                        local dinosaur = pad:FindFirstChild("Dinosaur")
                        if dinosaur then
                            for _, dino in pairs(dinosaur:GetChildren()) do
                                local proxPart = dino:FindFirstChild("ProxPart")
                                if proxPart then
                                    table.insert(DinosaurList, {
                                        Name = dino.Name,
                                        Base = basePath.Name,
                                        Pad = pad.Name,
                                        Position = proxPart.Position,
                                        FullPath = basePath.Name .. " - " .. pad.Name .. " - " .. dino.Name
                                    })
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

-- Initialize
FindMyBase()
ScanDinosaurs()

-- Create Rayfield Window
local Window = Rayfield:CreateWindow({
    Name = "StealADino Script",
    LoadingTitle = "Loading StealADino Script",
    LoadingSubtitle = "by WhyUSOmaddKid",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "StealADino",
        FileName = "Config"
    },
    Discord = {
        Enabled = false,
        Invite = "",
        RememberJoins = true
    },
    KeySystem = false
})

-- Main Tab
local MainTab = Window:CreateTab("Main", 4483362458)

-- Base Info Section
local BaseSection = MainTab:CreateSection("Base Information")

local BaseLabel = MainTab:CreateLabel("My Base: " .. (MyBaseName or "Not Found"))

local RefreshButton = MainTab:CreateButton({
    Name = "Refresh Base Info",
    Callback = function()
        local foundBase = FindMyBase()
        ScanDinosaurs()

        -- Update label
        BaseLabel:Set("My Base: " .. (MyBaseName or "Not Found"))

        if foundBase then
            Rayfield:Notify({
                Title = "Base Found!",
                Content = "Your base is: " .. foundBase,
                Duration = 3,
                Image = 4483362458
            })
        else
            Rayfield:Notify({
                Title = "Base Not Found",
                Content = "Could not find base with owner: WhyUSOmaddKid",
                Duration = 5,
                Image = 4483362458
            })
        end
    end,
})

-- Debug button untuk melihat semua base owners
local DebugButton = MainTab:CreateButton({
    Name = "Debug: Show All Base Owners",
    Callback = function()
        print("=== DEBUG: All Base Owners ===")
        for i = 1, 9 do
            local basePath = Workspace.Bases:FindFirstChild("Base" .. i)
            if basePath then
                local ownerValue = basePath:FindFirstChild("Owner")
                if ownerValue then
                    print("Base" .. i .. ":")
                    print("  ClassName:", ownerValue.ClassName)
                    print("  Name:", ownerValue.Name)
                    print("  Value:", tostring(ownerValue.Value))
                    print("  Value Type:", type(ownerValue.Value))
                else
                    print("Base" .. i .. ": No Owner found")
                end
            else
                print("Base" .. i .. ": Base not found")
            end
        end
        print("=== END DEBUG ===")
    end,
})

-- Teleport Section
local TeleportSection = MainTab:CreateSection("Teleportation")

local TeleportToBaseButton = MainTab:CreateButton({
    Name = "Teleport to My Base (Numpad 0)",
    Callback = function()
        TeleportToMyBase()
    end,
})

-- Auto Lock Section
local AutoLockSection = MainTab:CreateSection("Auto Lock")

local AutoLockButton = MainTab:CreateButton({
    Name = "Manual Lock My Base",
    Callback = function()
        local success = AutoLockBase()
        if success then
            Rayfield:Notify({
                Title = "Lock Success",
                Content = "Successfully locked your base!",
                Duration = 3,
                Image = 4483362458
            })
        else
            Rayfield:Notify({
                Title = "Lock Failed",
                Content = "Failed to lock base. Check console for details.",
                Duration = 3,
                Image = 4483362458
            })
        end
    end,
})

local AutoLockToggle = MainTab:CreateToggle({
    Name = "Auto Lock Base (Continuous)",
    CurrentValue = false,
    Flag = "AutoLockToggle",
    Callback = function(Value)
        AutoLockEnabled = Value
        if Value then
            Rayfield:Notify({
                Title = "Auto Lock Enabled",
                Content = "Auto lock will run every 5 seconds",
                Duration = 3,
                Image = 4483362458
            })
        else
            Rayfield:Notify({
                Title = "Auto Lock Disabled",
                Content = "Auto lock has been turned off",
                Duration = 3,
                Image = 4483362458
            })
        end
    end,
})

-- Debug button untuk melihat semua RemoteEvents
local DebugRemoteButton = MainTab:CreateButton({
    Name = "Debug: Show All RemoteEvents",
    Callback = function()
        print("=== DEBUG: All RemoteEvents ===")
        for _, child in pairs(game.ReplicatedStorage:GetChildren()) do
            if child:IsA("RemoteEvent") then
                print("RemoteEvent found:", child.Name)
            elseif child:IsA("RemoteFunction") then
                print("RemoteFunction found:", child.Name)
            end
        end
        print("=== END DEBUG ===")

        Rayfield:Notify({
            Title = "Debug Complete",
            Content = "Check console for RemoteEvents list",
            Duration = 3,
            Image = 4483362458
        })
    end,
})

-- Test RemoteEvent button
local TestRemoteButton = MainTab:CreateButton({
    Name = "Test: Fire Send RemoteEvent",
    Callback = function()
        pcall(function()
            local args = {
                true,
                "You locked your base for 60 seconds!"
            }
            game.ReplicatedStorage.Send:FireServer(unpack(args))
            print("Test: Fired Send RemoteEvent")

            Rayfield:Notify({
                Title = "Test Fired",
                Content = "Send RemoteEvent fired successfully!",
                Duration = 3,
                Image = 4483362458
            })
        end)
    end,
})

-- Dinosaur Tab
local DinoTab = Window:CreateTab("Dinosaurs", 4483362458)

local DinoSection = DinoTab:CreateSection("Available Dinosaurs")

-- Selected dinosaur info
local SelectedDinoLabel = DinoTab:CreateLabel("Selected: None")

local function UpdateSelectedDinoInfo()
    if SelectedDinosaur then
        SelectedDinoLabel:Set("Selected: " .. SelectedDinosaur.Name .. " (" .. SelectedDinosaur.Base .. " - " .. SelectedDinosaur.Pad .. ")")
    else
        SelectedDinoLabel:Set("Selected: None")
    end
end

-- Variables for dinosaur selection
local SelectedDinosaur = nil
local DinosaurDropdown = nil
local TeleportButton = nil
local DinoCountLabel = nil

-- Create dropdown for dinosaurs
local function UpdateDinosaurDropdown()
    local dinoOptions = {}
    for _, dino in pairs(DinosaurList) do
        table.insert(dinoOptions, dino.FullPath)
    end

    -- Update count label
    if DinoCountLabel then
        DinoCountLabel:Set("Dinosaurs Found: " .. #DinosaurList)
    else
        DinoCountLabel = DinoTab:CreateLabel("Dinosaurs Found: " .. #DinosaurList)
    end

    -- Always create teleport button first (so it appears below dropdown)
    if not TeleportButton then
        TeleportButton = DinoTab:CreateButton({
            Name = "🚀 Teleport to Selected Dinosaur",
            Callback = function()
                if SelectedDinosaur then
                    print("Teleporting to:", SelectedDinosaur.Name, "at position:", SelectedDinosaur.Position)
                    TeleportToPosition(SelectedDinosaur.Position + Vector3.new(0, 5, 0))
                    Rayfield:Notify({
                        Title = "Teleported!",
                        Content = "Teleported to " .. SelectedDinosaur.Name .. " at " .. SelectedDinosaur.Base,
                        Duration = 3,
                        Image = 4483362458
                    })
                else
                    Rayfield:Notify({
                        Title = "No Selection",
                        Content = "Please select a dinosaur first!",
                        Duration = 3,
                        Image = 4483362458
                    })
                end
            end,
        })
    end

    if #dinoOptions > 0 then
        -- Create or update dropdown
        if not DinosaurDropdown then
            DinosaurDropdown = DinoTab:CreateDropdown({
                Name = "Select Dinosaur Target",
                Options = dinoOptions,
                CurrentOption = dinoOptions[1] or "None",
                Callback = function(Option)
                    print("Dropdown selected:", Option)
                    -- Find and store selected dinosaur
                    for _, dino in pairs(DinosaurList) do
                        if dino.FullPath == Option then
                            SelectedDinosaur = dino
                            print("Selected dinosaur:", dino.Name, "at", dino.Base, dino.Pad)
                            UpdateSelectedDinoInfo()
                            break
                        end
                    end
                end,
            })
        else
            -- Update existing dropdown options
            DinosaurDropdown:Refresh(dinoOptions, dinoOptions[1] or "None")
        end

        -- Set initial selected dinosaur
        if not SelectedDinosaur and #DinosaurList > 0 then
            SelectedDinosaur = DinosaurList[1]
            print("Initial selected dinosaur:", SelectedDinosaur.Name)
            UpdateSelectedDinoInfo()
        end

    else
        -- Update count label for no dinosaurs
        if DinoCountLabel then
            DinoCountLabel:Set("No dinosaurs found. Scanning...")
        end
    end
end

-- Manual refresh button
local RefreshDinoButton = DinoTab:CreateButton({
    Name = "🔄 Refresh Dinosaur List",
    Callback = function()
        ScanDinosaurs()
        UpdateDinosaurDropdown()
        Rayfield:Notify({
            Title = "Refreshed",
            Content = "Dinosaur list updated! Found " .. #DinosaurList .. " dinosaurs",
            Duration = 3,
            Image = 4483362458
        })
    end,
})

-- Auto-refresh toggle
local AutoRefreshToggle = DinoTab:CreateToggle({
    Name = "Auto Refresh Dinosaur List",
    CurrentValue = true,
    Flag = "AutoRefreshDino",
    Callback = function(Value)
        if Value then
            Rayfield:Notify({
                Title = "Auto Refresh ON",
                Content = "Dinosaur list will update every 10 seconds",
                Duration = 3,
                Image = 4483362458
            })
        else
            Rayfield:Notify({
                Title = "Auto Refresh OFF",
                Content = "Manual refresh only",
                Duration = 3,
                Image = 4483362458
            })
        end
    end,
})

-- Debug button untuk melihat selected dinosaur
local DebugDinoButton = DinoTab:CreateButton({
    Name = "Debug: Show Selected Dinosaur",
    Callback = function()
        if SelectedDinosaur then
            print("=== SELECTED DINOSAUR DEBUG ===")
            print("Name:", SelectedDinosaur.Name)
            print("Base:", SelectedDinosaur.Base)
            print("Pad:", SelectedDinosaur.Pad)
            print("Position:", SelectedDinosaur.Position)
            print("FullPath:", SelectedDinosaur.FullPath)
            print("=== END DEBUG ===")

            Rayfield:Notify({
                Title = "Debug Info",
                Content = "Selected: " .. SelectedDinosaur.Name .. " at " .. SelectedDinosaur.Base,
                Duration = 3,
                Image = 4483362458
            })
        else
            print("No dinosaur selected!")
            Rayfield:Notify({
                Title = "Debug Info",
                Content = "No dinosaur selected!",
                Duration = 3,
                Image = 4483362458
            })
        end
    end,
})

-- Initial update
UpdateDinosaurDropdown()

-- Keybind for Numpad 0
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.KeypadZero then
        TeleportToMyBase()
    end
end)

-- Auto-refresh dinosaur list every 10 seconds (if enabled)
spawn(function()
    while true do
        wait(10)
        -- Check if auto refresh is enabled (default true)
        local autoRefreshEnabled = Rayfield.Flags["AutoRefreshDino"]
        if autoRefreshEnabled == nil or autoRefreshEnabled == true then
            local oldCount = #DinosaurList
            ScanDinosaurs()
            local newCount = #DinosaurList

            -- Update UI if count changed
            if oldCount ~= newCount then
                UpdateDinosaurDropdown()
                print("Auto-refresh: Dinosaur count changed from", oldCount, "to", newCount)
            end
        end
    end
end)

-- Auto Lock Loop
spawn(function()
    while true do
        wait(5) -- Check every 5 seconds
        if AutoLockEnabled and MyBaseName then
            AutoLockBase()
        end
    end
end)

Rayfield:Notify({
    Title = "Script Loaded",
    Content = "StealADino script loaded successfully!",
    Duration = 5,
    Image = 4483362458
})
