-- StealADino Script with Rayfield UI
local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Services
local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Variables
local LocalPlayer = Players.LocalPlayer
local Character = LocalPlayer.Character or LocalPlayer.CharacterAdded:Wait()
local Humanoid = Character:WaitForChild("Humanoid")
local HumanoidRootPart = Character:WaitForChild("HumanoidRootPart")

local MyBaseName = nil
local MyBaseNumber = nil
local DinosaurList = {}

-- Functions
local function FindMyBase()
    local targetOwner = "WhyUSOmaddKid"
    
    for i = 1, 9 do
        local basePath = Workspace.Bases:FindFirstChild("Base" .. i)
        if basePath then
            local ownerValue = basePath:FindFirstChild("Owner")
            if ownerValue and ownerValue.ClassName == "ObjectValue" and ownerValue.Value == targetOwner then
                MyBaseName = "Base" .. i
                MyBaseNumber = i
                return MyBaseName
            end
        end
    end
    return nil
end

local function TeleportToPosition(position)
    if HumanoidRootPart then
        HumanoidRootPart.CFrame = CFrame.new(position)
    end
end

local function TeleportToMyBase()
    if MyBaseName then
        local myBase = Workspace.Bases:FindFirstChild(MyBaseName)
        if myBase then
            local collectZone = myBase:FindFirstChild("CollectZone")
            if collectZone then
                local collect = collectZone:FindFirstChild("Collect")
                if collect then
                    TeleportToPosition(collect.Position + Vector3.new(0, 5, 0))
                end
            end
        end
    end
end

local function AutoLockBase()
    if MyBaseName then
        local myBase = Workspace.Bases:FindFirstChild(MyBaseName)
        if myBase then
            local lockBase = myBase:FindFirstChild("LockBase")
            if lockBase then
                local proximityPrompt = lockBase:FindFirstChild("ProximityPrompt")
                if proximityPrompt then
                    fireproximityprompt(proximityPrompt)
                end
            end
        end
    end
end

local function ScanDinosaurs()
    DinosaurList = {}
    
    for i = 1, 9 do
        local basePath = Workspace.Bases:FindFirstChild("Base" .. i)
        if basePath and basePath.Name ~= MyBaseName then
            local pads = basePath:FindFirstChild("Pads")
            if pads then
                for _, pad in pairs(pads:GetChildren()) do
                    if pad.Name:match("Pad%d+") then
                        local dinosaur = pad:FindFirstChild("Dinosaur")
                        if dinosaur then
                            for _, dino in pairs(dinosaur:GetChildren()) do
                                local proxPart = dino:FindFirstChild("ProxPart")
                                if proxPart then
                                    table.insert(DinosaurList, {
                                        Name = dino.Name,
                                        Base = basePath.Name,
                                        Pad = pad.Name,
                                        Position = proxPart.Position,
                                        FullPath = basePath.Name .. " - " .. pad.Name .. " - " .. dino.Name
                                    })
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

-- Initialize
FindMyBase()
ScanDinosaurs()

-- Create Rayfield Window
local Window = Rayfield:CreateWindow({
    Name = "StealADino Script",
    LoadingTitle = "Loading StealADino Script",
    LoadingSubtitle = "by WhyUSOmaddKid",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "StealADino",
        FileName = "Config"
    },
    Discord = {
        Enabled = false,
        Invite = "",
        RememberJoins = true
    },
    KeySystem = false
})

-- Main Tab
local MainTab = Window:CreateTab("Main", 4483362458)

-- Base Info Section
local BaseSection = MainTab:CreateSection("Base Information")

MainTab:CreateLabel("My Base: " .. (MyBaseName or "Not Found"))

local RefreshButton = MainTab:CreateButton({
    Name = "Refresh Base Info",
    Callback = function()
        FindMyBase()
        ScanDinosaurs()
        Rayfield:Notify({
            Title = "Refreshed",
            Content = "Base info and dinosaur list updated!",
            Duration = 3,
            Image = 4483362458
        })
    end,
})

-- Teleport Section
local TeleportSection = MainTab:CreateSection("Teleportation")

local TeleportToBaseButton = MainTab:CreateButton({
    Name = "Teleport to My Base (Numpad 0)",
    Callback = function()
        TeleportToMyBase()
    end,
})

-- Auto Lock Section
local AutoLockSection = MainTab:CreateSection("Auto Lock")

local AutoLockButton = MainTab:CreateButton({
    Name = "Auto Lock My Base",
    Callback = function()
        AutoLockBase()
        Rayfield:Notify({
            Title = "Auto Lock",
            Content = "Attempted to lock your base!",
            Duration = 3,
            Image = 4483362458
        })
    end,
})

-- Dinosaur Tab
local DinoTab = Window:CreateTab("Dinosaurs", 4483362458)

local DinoSection = DinoTab:CreateSection("Available Dinosaurs")

-- Create dropdown for dinosaurs
local function UpdateDinosaurDropdown()
    local dinoOptions = {}
    for _, dino in pairs(DinosaurList) do
        table.insert(dinoOptions, dino.FullPath)
    end
    
    if #dinoOptions > 0 then
        local DinosaurDropdown = DinoTab:CreateDropdown({
            Name = "Select Dinosaur to Teleport",
            Options = dinoOptions,
            CurrentOption = dinoOptions[1] or "None",
            Callback = function(Option)
                for _, dino in pairs(DinosaurList) do
                    if dino.FullPath == Option then
                        TeleportToPosition(dino.Position + Vector3.new(0, 5, 0))
                        Rayfield:Notify({
                            Title = "Teleported",
                            Content = "Teleported to " .. dino.Name .. " at " .. dino.Base,
                            Duration = 3,
                            Image = 4483362458
                        })
                        break
                    end
                end
            end,
        })
    else
        DinoTab:CreateLabel("No dinosaurs found. Try refreshing.")
    end
end

UpdateDinosaurDropdown()

-- Keybind for Numpad 0
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.KeypadZero then
        TeleportToMyBase()
    end
end)

-- Auto-refresh dinosaur list every 30 seconds
spawn(function()
    while true do
        wait(30)
        ScanDinosaurs()
    end
end)

Rayfield:Notify({
    Title = "Script Loaded",
    Content = "StealADino script loaded successfully!",
    Duration = 5,
    Image = 4483362458
})
